import pygame
class Snake:
    def __init__(self, desktop_size):
        self.head_direction = 'r'
        self.len = 2
        self.x = int(desktop_size[0] - desktop_size[0] / 2 + self.len / 2)
        self.y = int(desktop_size[1] - desktop_size[1] / 2 + self.len / 2)
        self.tail_len = self.len - 1
        self.tail_position = [{"x": self.x - 10, "y": self.y}]
        tail = self.tail_position[0]
    def tick(self, key, screen):
        if key == 'up':
            if self.head_direction == 'd':
                self.head_direction = self.head_direction
            self.head_direction == 'u'
            self.y -= 10
            for tail in self.tail_position:
                tail['x'] += 10
            self.draw(screen)
        if self.head_direction == 'r':
            self.tail_position = [{self.x - 10, self.y}]
        elif self.head_direction == 'l':
            self.tail_position = [{self.x + 10, self.y}]     
        elif self.head_direction == 'd':
            self.tail_position = [{self.x, self.y - 10}]
    def draw(self, screen):
        pygame.Surface.fill(screen, "darkgreen", ((self.x, self.y), (10, 10)))
        for tail in self.tail_position:
            pygame.Surface.fill(screen, "lightgreen", ((tail["x"], tail.y), (10, 10)))
        