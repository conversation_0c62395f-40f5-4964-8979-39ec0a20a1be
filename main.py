import pygame
from snake import Snake
pygame.init()

desktop_size = pygame.display.get_desktop_sizes()[0]
screen = pygame.display.set_mode(desktop_size, pygame.FULLSCREEN)
clock = pygame.time.Clock()
key = ''
running = True
while running:
    snake = Snake(desktop_size)
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
        if event.type == pygame.KEYDOWN:
            key = pygame.key.name(event.key)

    screen.fill((240, 240, 252))
    snake.__init__(desktop_size)
    snake.tick(key, screen)
    snake.draw(screen)
    pygame.display.flip()

    clock.tick(60)

pygame.quit()